"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

export function FinalCTA() {
  const [formData, setFormData] = useState({
    email: "",
    linkedin: "",
    painPoint: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (formData.email && formData.linkedin) {
      alert(
        `Thank you! We've added ${formData.email} to our waitlist. You'll be among the first to know when we launch!`,
      )
      setFormData({ email: "", linkedin: "", painPoint: "" })
    }
  }

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" })
    }
  }

  return (
    <section id="waitlist" className="py-20 bg-gradient-to-br from-blue-50 to-cyan-50">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Tired of your job applications getting ignored?
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Join our community to be among the first to access SkillVerdict and see how it works
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Button onClick={() => scrollToSection("for-candidates")} className="bg-primary hover:bg-primary/90">
              Verify my skills
            </Button>
            <Button onClick={() => scrollToSection("for-vetters")} className="bg-primary hover:bg-primary/90">
              Earn by verifying
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              type="email"
              placeholder="Your email address *"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              required
              className="h-12"
            />
            <Input
              type="url"
              placeholder="Your LinkedIn profile URL *"
              value={formData.linkedin}
              onChange={(e) => setFormData({ ...formData, linkedin: e.target.value })}
              required
              className="h-12"
            />
            <Textarea
              placeholder="Biggest pain point with job hunting (optional)"
              value={formData.painPoint}
              onChange={(e) => setFormData({ ...formData, painPoint: e.target.value })}
              className="min-h-[100px] resize-none"
            />
            <Button type="submit" className="w-full h-12 bg-primary hover:bg-primary/90">
              Secure My Spot
            </Button>
          </form>
        </div>
      </div>
    </section>
  )
}
